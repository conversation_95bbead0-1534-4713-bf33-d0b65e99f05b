import type { NextPage } from 'next';
import Head from 'next/head';

import { Button } from '@/components/ui/Button';

const Home: NextPage = () => {
  const handleClick = (): void => {
    console.error('But<PERSON> clicked!');
  };

  return (
    <>
      <Head>
        <title>Next.js TypeScript Boilerplate</title>
        <meta name='description' content='A strict TypeScript Next.js boilerplate' />
        <meta name='viewport' content='width=device-width, initial-scale=1' />
        <link rel='icon' href='/favicon.ico' />
      </Head>
      <main className='flex min-h-screen flex-col items-center justify-center p-24'>
        <div className='z-10 w-full max-w-5xl items-center justify-between font-mono text-sm lg:flex'>
          <h1 className='mb-8 text-4xl font-bold'>
            Welcome to Next.js TypeScript Boilerplate
          </h1>
        </div>
        <div className='mb-32 grid text-center lg:mb-0 lg:grid-cols-4 lg:text-left'>
          <div className='group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100 hover:dark:border-neutral-700 hover:dark:bg-neutral-800/30'>
            <h2 className='mb-3 text-2xl font-semibold'>
              Strict TypeScript{' '}
              <span className='inline-block transition-transform group-hover:translate-x-1 motion-reduce:transform-none'>
                -&gt;
              </span>
            </h2>
            <p className='m-0 max-w-[30ch] text-sm opacity-50'>
              Configured with strict TypeScript rules and file extension enforcement.
            </p>
          </div>
          <div className='group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100 hover:dark:border-neutral-700 hover:dark:bg-neutral-800/30'>
            <h2 className='mb-3 text-2xl font-semibold'>
              ESLint Rules{' '}
              <span className='inline-block transition-transform group-hover:translate-x-1 motion-reduce:transform-none'>
                -&gt;
              </span>
            </h2>
            <p className='m-0 max-w-[30ch] text-sm opacity-50'>
              Comprehensive ESLint configuration with best practices and file naming conventions.
            </p>
          </div>
          <div className='group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100 hover:dark:border-neutral-700 hover:dark:bg-neutral-800/30'>
            <h2 className='mb-3 text-2xl font-semibold'>
              Tailwind CSS{' '}
              <span className='inline-block transition-transform group-hover:translate-x-1 motion-reduce:transform-none'>
                -&gt;
              </span>
            </h2>
            <p className='m-0 max-w-[30ch] text-sm opacity-50'>
              Pre-configured with Tailwind CSS and custom design system variables.
            </p>
          </div>
          <div className='group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100 hover:dark:border-neutral-700 hover:dark:bg-neutral-800/30'>
            <h2 className='mb-3 text-2xl font-semibold'>
              Development Tools{' '}
              <span className='inline-block transition-transform group-hover:translate-x-1 motion-reduce:transform-none'>
                -&gt;
              </span>
            </h2>
            <p className='m-0 max-w-[30ch] text-sm opacity-50'>
              Prettier, type checking, and development scripts ready to use.
            </p>
          </div>
        </div>
        <Button onClick={handleClick}>Get Started</Button>
      </main>
    </>
  );
};

export default Home;
