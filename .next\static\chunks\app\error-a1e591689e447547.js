(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[39],{1065:(e,r,n)=>{"use strict";n.r(r),n.d(r,{default:()=>d});var t=n(5155),o=n(2115);function i(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var l=function(e){let r=function(e){let r=o.forwardRef((e,r)=>{let{children:n,...t}=e;if(o.isValidElement(n)){var l,c,s;let e,a,d=(a=(e=null==(c=Object.getOwnPropertyDescriptor((l=n).props,"ref"))?void 0:c.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(a=(e=null==(s=Object.getOwnPropertyDescriptor(l,"ref"))?void 0:s.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref,u=function(e,r){let n={...r};for(let t in r){let o=e[t],i=r[t];/^on[A-Z]/.test(t)?o&&i?n[t]=function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];let t=i(...r);return o(...r),t}:o&&(n[t]=o):"style"===t?n[t]={...o,...i}:"className"===t&&(n[t]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(t,n.props);return n.type!==o.Fragment&&(u.ref=r?function(...e){return r=>{let n=!1,t=e.map(e=>{let t=i(e,r);return n||"function"!=typeof t||(n=!0),t});if(n)return()=>{for(let r=0;r<t.length;r++){let n=t[r];"function"==typeof n?n():i(e[r],null)}}}}(r,d):d),o.cloneElement(n,u)}return o.Children.count(n)>1?o.Children.only(null):null});return r.displayName="".concat(e,".SlotClone"),r}(e),n=o.forwardRef((e,n)=>{let{children:i,...l}=e,c=o.Children.toArray(i),a=c.find(s);if(a){let e=a.props.children,i=c.map(r=>r!==a?r:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,t.jsx)(r,{...l,ref:n,children:o.isValidElement(e)?o.cloneElement(e,void 0,i):null})}return(0,t.jsx)(r,{...l,ref:n,children:i})});return n.displayName="".concat(e,".Slot"),n}("Slot"),c=Symbol("radix.slottable");function s(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===c}let a=(0,o.forwardRef)((e,r)=>{let{className:n="",variant:o="default",size:i="default",asChild:c=!1,children:s,...a}=e,d="".concat("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"," ").concat({default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"}[o]," ").concat({default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}[i]," ").concat(n);return(0,t.jsx)(c?l:"button",{className:d,ref:r,...a,children:s})});function d(e){let{error:r,reset:n}=e;return(0,o.useEffect)(()=>{console.error("Application error:",r)},[r]),(0,t.jsx)("div",{className:"flex min-h-screen flex-col items-center justify-center p-8",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"mb-4 text-4xl font-bold text-destructive",children:"Something went wrong!"}),(0,t.jsx)("p",{className:"mb-8 text-muted-foreground",children:"An unexpected error occurred. Please try again."}),(0,t.jsxs)("div",{className:"space-x-4",children:[(0,t.jsx)(a,{onClick:n,children:"Try again"}),(0,t.jsx)(a,{variant:"outline",onClick:()=>window.location.href="/",children:"Go home"})]}),!1]})})}a.displayName="Button"},4416:(e,r,n)=>{Promise.resolve().then(n.bind(n,1065))}},e=>{e.O(0,[441,964,358],()=>e(e.s=4416)),_N_E=e.O()}]);