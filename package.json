{"name": "nextjs-typescript-boilerplate", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "format": "prettier --write .", "format:check": "prettier --check .", "pre-commit": "npm run type-check && npm run lint && npm run format:check", "clean": "rm -rf .next out dist", "analyze": "cross-env ANALYZE=true next build"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@next/bundle-analyzer": "^14.0.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-check-file": "^2.6.2", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-tailwindcss": "^3.13.0", "eslint-plugin-unused-imports": "^3.0.0", "postcss": "^8.4.31", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.7", "tailwindcss": "^3.3.5", "typescript": "^5.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}