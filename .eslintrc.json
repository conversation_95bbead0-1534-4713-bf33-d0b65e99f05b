{
  "extends": [
    "next/core-web-vitals",
    "plugin:@typescript-eslint/recommended",
    "plugin:@typescript-eslint/recommended-requiring-type-checking",
    "plugin:@typescript-eslint/strict",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:jsx-a11y/recommended",
    "plugin:import/recommended",
    "plugin:import/typescript",
    "prettier",
    "plugin:tailwindcss/recommended"
  ],
  "plugins": [
    "check-file",
    "@typescript-eslint",
    "react",
    "react-hooks",
    "jsx-a11y",
    "import",
    "unused-imports"
  ],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": "latest",
    "sourceType": "module",
    "project": "./tsconfig.json",
    "ecmaFeatures": {
      "jsx": true
    }
  },
  "settings": {
    "react": {
      "version": "detect"
    },
    "import/resolver": {
      "typescript": {
        "alwaysTryTypes": true,
        "project": "./tsconfig.json"
      }
    }
  },
  "rules": {
    /* TypeScript Strict Rules */
    "@typescript-eslint/no-explicit-any": "error",
    "@typescript-eslint/no-unused-vars": "off",
    "@typescript-eslint/no-use-before-define": "error",
    "@typescript-eslint/explicit-function-return-type": "error",
    "@typescript-eslint/explicit-module-boundary-types": "error",
    "@typescript-eslint/no-non-null-assertion": "error",
    "@typescript-eslint/prefer-nullish-coalescing": "error",
    "@typescript-eslint/prefer-optional-chain": "error",
    "@typescript-eslint/no-unnecessary-type-assertion": "error",
    "@typescript-eslint/no-floating-promises": "error",
    "@typescript-eslint/await-thenable": "error",
    "@typescript-eslint/no-misused-promises": "error",
    "@typescript-eslint/require-await": "error",
    "@typescript-eslint/no-unsafe-assignment": "error",
    "@typescript-eslint/no-unsafe-call": "error",
    "@typescript-eslint/no-unsafe-member-access": "error",
    "@typescript-eslint/no-unsafe-return": "error",
    "@typescript-eslint/restrict-template-expressions": "error",
    "@typescript-eslint/prefer-as-const": "error",
    "@typescript-eslint/consistent-type-definitions": ["error", "interface"],
    "@typescript-eslint/consistent-type-imports": [
      "error",
      {
        "prefer": "type-imports",
        "disallowTypeAnnotations": false
      }
    ],

    /* File and Folder Naming Rules */
    "check-file/filename-blocklist": [
      "error",
      {
        "**/*.js": "*.ts",
        "**/*.jsx": "*.tsx",
        "**/*.model.ts": "*.models.ts",
        "**/*.util.ts": "*.utils.ts"
      }
    ],
    "check-file/filename-naming-convention": [
      "error",
      {
        "src/**/*.{ts,tsx}": "+(+([a-z0-9])|*([A-Z]*([a-z0-9])))",
        "src/components/**/*.{ts,tsx}": "PASCAL_CASE",
        "src/pages/**/*.{ts,tsx}": "KEBAB_CASE",
        "src/utils/**/*.ts": "CAMEL_CASE",
        "src/hooks/**/*.ts": "CAMEL_CASE",
        "src/types/**/*.ts": "CAMEL_CASE"
      }
    ],
    "check-file/folder-match-with-fex": [
      "error",
      {
        "*.test.{ts,tsx}": "**/__tests__/",
        "*.spec.{ts,tsx}": "**/__tests__/"
      }
    ],
    "check-file/folder-naming-convention": [
      "error",
      {
        "src/*/": "KEBAB_CASE",
        "components/*/": "KEBAB_CASE"
      }
    ],

    /* Import Rules */
    "import/order": [
      "error",
      {
        "groups": [
          "builtin",
          "external",
          "internal",
          "parent",
          "sibling",
          "index"
        ],
        "newlines-between": "always",
        "alphabetize": {
          "order": "asc",
          "caseInsensitive": true
        }
      }
    ],
    "import/no-unresolved": "error",
    "import/no-cycle": "error",
    "import/no-self-import": "error",
    "import/no-useless-path-segments": "error",
    "import/prefer-default-export": "off",
    "import/no-default-export": "off",

    /* Unused Imports */
    "unused-imports/no-unused-imports": "error",
    "unused-imports/no-unused-vars": [
      "error",
      {
        "vars": "all",
        "varsIgnorePattern": "^_",
        "args": "after-used",
        "argsIgnorePattern": "^_",
        "caughtErrorsIgnorePattern": "^_"
      }
    ],

    /* React Rules */
    "react/react-in-jsx-scope": "off",
    "react/prop-types": "off",
    "react/jsx-sort-props": [
      "error",
      {
        "callbacksLast": true,
        "ignoreCase": true,
        "noSortAlphabetically": false,
        "shorthandFirst": false,
        "shorthandLast": true
      }
    ],
    "react/no-unknown-property": [
      "error",
      {
        "ignore": ["jsx"]
      }
    ],
    "react/jsx-no-useless-fragment": "error",
    "react/jsx-curly-brace-presence": [
      "error",
      {
        "props": "never",
        "children": "never"
      }
    ],
    "react/self-closing-comp": "error",
    "react/jsx-boolean-value": ["error", "never"],

    /* General Rules */
    "no-console": [
      "error",
      {
        "allow": ["error", "warn"]
      }
    ],
    "no-unused-vars": "off",
    "prefer-const": "error",
    "no-var": "error",
    "object-shorthand": "error",
    "prefer-arrow-callback": "error",
    "prefer-template": "error",
    "no-duplicate-imports": "error",

    /* Accessibility Rules */
    "jsx-a11y/alt-text": "error",
    "jsx-a11y/anchor-has-content": "error",
    "jsx-a11y/anchor-is-valid": "error",
    "jsx-a11y/click-events-have-key-events": "error",
    "jsx-a11y/no-static-element-interactions": "error",

    /* Tailwind Rules */
    "tailwindcss/classnames-order": "off",
    "tailwindcss/no-custom-classname": "off",
    "tailwindcss/enforces-negative-arbitrary-values": "error",
    "tailwindcss/enforces-shorthand": "error"
  },
  "overrides": [
    {
      "files": ["src/pages/**/*.{ts,tsx}", "src/app/**/*.{ts,tsx}"],
      "rules": {
        "import/no-default-export": "off"
      }
    },
    {
      "files": ["*.config.{ts,js}", "*.d.ts"],
      "rules": {
        "@typescript-eslint/explicit-function-return-type": "off",
        "@typescript-eslint/explicit-module-boundary-types": "off"
      }
    }
  ]
}
